#!/usr/bin/env python3
"""
Fix for Tamil response corruption issue.
This script identifies and fixes the Tamil text processing problems.
"""

import sys
import os
import re
from typing import Dict, Any, <PERSON><PERSON>

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def improved_tamil_corruption_detection(text: str) -> Tuple[bool, str]:
    """
    Improved Tamil text corruption detection that's more accurate for Tamil script.
    
    Args:
        text: The text to analyze
        
    Returns:
        tuple: (is_corrupted, cleaned_text)
    """
    if not text or len(text.strip()) <= 10:
        return False, text
    
    # Check for excessive character repetition (common corruption pattern)
    def detect_char_repetition(text):
        """Detect excessive character repetition"""
        if len(text) < 10:
            return False, text
            
        # Look for patterns where the same character repeats more than 5 times
        repetition_pattern = re.compile(r'(.)\1{5,}')
        matches = repetition_pattern.findall(text)
        
        if matches:
            print(f"🚨 Detected excessive character repetition: {matches}")
            # Remove excessive repetitions, keeping only 2 occurrences
            cleaned = re.sub(r'(.)\1{5,}', r'\1\1', text)
            return True, cleaned
        
        return False, text
    
    # Check for word repetition specific to Tamil
    def detect_tamil_word_repetition(text):
        """Detect word repetition in Tamil text"""
        # Split by Tamil word boundaries (spaces and punctuation)
        words = re.findall(r'[^\s\u0020-\u002F\u003A-\u0040\u005B-\u0060\u007B-\u007E]+', text)
        
        if len(words) < 3:
            return False, text
        
        cleaned_words = []
        repetition_count = 0
        i = 0
        
        while i < len(words):
            current_word = words[i]
            
            # Check for consecutive identical words
            consecutive_count = 1
            j = i + 1
            while j < len(words) and words[j] == current_word:
                consecutive_count += 1
                j += 1
            
            # If more than 2 consecutive identical words, it's likely corruption
            if consecutive_count > 2:
                repetition_count += consecutive_count - 1
                cleaned_words.append(current_word)  # Keep only one instance
                print(f"🔧 Removed {consecutive_count - 1} repetitions of Tamil word: '{current_word}'")
                i = j
            else:
                cleaned_words.append(current_word)
                i += 1
        
        repetition_ratio = repetition_count / len(words) if len(words) > 0 else 0
        is_corrupted = repetition_ratio > 0.15  # Higher threshold for Tamil
        
        if is_corrupted:
            print(f"🚨 Tamil word repetition detected ({repetition_ratio:.1%})")
            return True, ' '.join(cleaned_words)
        
        return False, text
    
    # Apply both checks
    char_corrupted, text_after_char_clean = detect_char_repetition(text)
    word_corrupted, text_after_word_clean = detect_tamil_word_repetition(text_after_char_clean)
    
    is_corrupted = char_corrupted or word_corrupted
    final_text = text_after_word_clean
    
    return is_corrupted, final_text

def improved_tamil_response_generation(query: str, context_docs: list, selected_language: str = "Tamil") -> str:
    """
    Improved Tamil response generation with better prompts and corruption handling.
    
    Args:
        query: The Tamil query
        context_docs: Retrieved documents
        selected_language: Language for response
        
    Returns:
        Generated response in Tamil
    """
    # Extract context from documents
    context_texts = []
    source_info = []
    
    for doc in context_docs:
        if isinstance(doc, dict):
            metadata = doc.get('metadata', {})
            text = metadata.get('chunk_text', '')
            source_type = metadata.get('source_type', 'unknown')
            url = metadata.get('url', '')
            title = metadata.get('title', 'Unknown')
        else:
            text = doc.metadata.get('chunk_text', '')
            source_type = doc.metadata.get('source_type', 'unknown')
            url = doc.metadata.get('url', '')
            title = doc.metadata.get('title', 'Unknown')
        
        context_texts.append(text)
        
        source_entry = {
            'type': source_type,
            'url': url,
            'title': title
        }
        if source_entry not in source_info:
            source_info.append(source_entry)
    
    context = "\n\n".join(context_texts)
    
    # Create improved Tamil system prompt
    system_prompt = """நீங்கள் ஒரு நம்பகமான தமிழ் உதவியாளர். கேள்விகளுக்கு தெளிவான, துல்லியமான பதில்களை தமிழில் வழங்குங்கள்.

முக்கிய வழிமுறைகள்:
1. பதில் முழுவதும் தமிழில் மட்டுமே இருக்க வேண்டும்
2. கொடுக்கப்பட்ட தகவலின் அடிப்படையில் மட்டுமே பதிலளிக்கவும்
3. ஆதாரங்களை குறிப்பிடுங்கள் (எ.கா., "கட்டுரையின் படி...", "ஆவணத்தில் குறிப்பிட்டுள்ளபடி...")
4. தெளிவான, எளிய தமிழில் எழுதுங்கள்
5. தேவையற்ற வார்த்தை அல்லது வாக்கிய மீண்டும் சொல்லாதீர்கள்"""

    # Create improved user prompt
    user_prompt = f"""கொடுக்கப்பட்ட தகவலின் அடிப்படையில் பின்வரும் கேள்விக்கு தமிழில் பதிலளியுங்கள்:

சூழல் தகவல்:
{context}

கேள்வி: {query}

குறிப்பு: 
- பதில் தெளிவாகவும் துல்லியமாகவும் இருக்க வேண்டும்
- தமிழில் மட்டுமே பதிலளிக்கவும்
- ஆதாரங்களை குறிப்பிடுங்கள்"""

    return {
        'system_prompt': system_prompt,
        'user_prompt': user_prompt,
        'messages': [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    }

def fix_tamil_processing_in_main_code():
    """
    Generate the fixes that need to be applied to the main code.
    """
    print("🔧 TAMIL PROCESSING FIXES")
    print("=" * 60)
    
    fixes = {
        'corruption_detection': {
            'description': 'Improved Tamil-specific corruption detection',
            'location': 'detect_text_corruption function',
            'fix': '''
# Replace the existing detect_text_corruption function with improved Tamil handling
def detect_text_corruption(text, char_diversity):
    """
    Improved text corruption detection with better Tamil support.
    """
    if len(text.strip()) <= 10:
        return False, text
    
    # Tamil-specific corruption detection
    if is_tamil_text(text):
        return improved_tamil_corruption_detection(text)
    
    # Original logic for other languages
    word_corrupted, cleaned_text = detect_word_repetition(text)
    # ... rest of original function
'''
        },
        
        'response_generation': {
            'description': 'Improved Tamil response generation',
            'location': 'generate_response function',
            'fix': '''
# In generate_response function, improve Tamil prompts:
if selected_language == "Tamil":
    system_prompt = """நீங்கள் ஒரு நம்பகமான தமிழ் உதவியாளர். கேள்விகளுக்கு தெளிவான, துல்லியமான பதில்களை தமிழில் வழங்குங்கள்.

முக்கிய வழிமுறைகள்:
1. பதில் முழுவதும் தமிழில் மட்டுமே இருக்க வேண்டும்
2. கொடுக்கப்பட்ட தகவலின் அடிப்படையில் மட்டுமே பதிலளிக்கவும்
3. ஆதாரங்களை குறிப்பிடுங்கள்
4. தெளிவான, எளிய தமிழில் எழுதுங்கள்
5. தேவையற்ற மீண்டும் சொல்லாதீர்கள்"""

    user_prompt = f"""கொடுக்கப்பட்ட தகவலின் அடிப்படையில் பின்வரும் கேள்விக்கு தமிழில் பதிலளியுங்கள்:

சூழல் தகவல்:
{context}

கேள்வி: {query}

குறிப்பு: பதில் தெளிவாகவும் துல்லியமாகவும் தமிழில் மட்டுமே இருக்க வேண்டும்."""
'''
        },
        
        'skip_translation_logic': {
            'description': 'Fix Tamil query processing to avoid unnecessary translation',
            'location': 'handle_query function',
            'fix': '''
# Improve the skip_translation logic for Tamil queries:
if selected_language == "Tamil" and (not requested_index_name or requested_index_name == "tamil" or requested_index_name == "default"):
    print(f"🌏 TAMIL QUERY: Using direct Tamil processing (no translation needed)")
    skip_translation = True
    search_query = original_query  # Use original Tamil query
'''
        }
    }
    
    return fixes

def test_tamil_corruption_detection():
    """Test the improved corruption detection with sample text."""
    print("\n🧪 TESTING IMPROVED TAMIL CORRUPTION DETECTION")
    print("=" * 60)
    
    # Test cases
    test_cases = [
        {
            'name': 'Normal Tamil text',
            'text': 'தமிழ்நாட்டில் முட்டை விலை அதிகரித்துள்ளது',
            'expected_corrupted': False
        },
        {
            'name': 'Character repetition corruption',
            'text': 'தமிழ்நாட்டில் முட்டையயயயயயயயயயய விலை',
            'expected_corrupted': True
        },
        {
            'name': 'Word repetition corruption',
            'text': 'தமிழ்நாட்டில் தமிழ்நாட்டில் தமிழ்நாட்டில் முட்டை விலை',
            'expected_corrupted': True
        },
        {
            'name': 'Mixed corruption',
            'text': 'வழங்கப்பட்ட சூழலில் தமிழ்நாடு அல்லது முட்டை விலைகள் குறித்த. எனவே, கொடுக்கப்பட்ட ஆதாரங்களின் அடிப்படையில் "தமிழ்நாட்டில் தமிழ்நாட்டில் முட்டை, கேரளா 228 பைசா"',
            'expected_corrupted': True
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📝 Testing: {test_case['name']}")
        print(f"   Text: {test_case['text'][:50]}...")
        
        is_corrupted, cleaned_text = improved_tamil_corruption_detection(test_case['text'])
        
        print(f"   Corrupted: {is_corrupted}")
        print(f"   Expected: {test_case['expected_corrupted']}")
        
        if is_corrupted:
            print(f"   Cleaned: {cleaned_text[:50]}...")
        
        status = "✅ PASS" if is_corrupted == test_case['expected_corrupted'] else "❌ FAIL"
        print(f"   Result: {status}")

def main():
    """Main function to run the Tamil processing fixes."""
    print("🚀 TAMIL RESPONSE CORRUPTION FIX")
    print("=" * 60)
    
    # Test the improved corruption detection
    test_tamil_corruption_detection()
    
    # Generate fixes for the main code
    fixes = fix_tamil_processing_in_main_code()
    
    print("\n🔧 REQUIRED FIXES FOR MAIN CODE:")
    print("=" * 60)
    
    for fix_name, fix_info in fixes.items():
        print(f"\n📍 {fix_info['description']}")
        print(f"   Location: {fix_info['location']}")
        print(f"   Fix:")
        print(fix_info['fix'])
        print("-" * 40)
    
    print("\n🎯 SUMMARY:")
    print("✅ Improved Tamil corruption detection")
    print("✅ Better Tamil response generation prompts")
    print("✅ Fixed skip_translation logic for Tamil")
    print("✅ Added Tamil-specific word repetition detection")
    
    print("\n📋 NEXT STEPS:")
    print("1. Apply the fixes above to your main full_code.py")
    print("2. Test with your Tamil query about egg prices")
    print("3. Monitor the debug output for corruption detection")
    print("4. Verify that Tamil responses are clean and relevant")

if __name__ == "__main__":
    main()